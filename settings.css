body {
  font-family: -apple-system, BlinkMacSystemFont, "San Francisco", "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  background: #f5f5f7;
  color: #1d1d1f;
  display: flex;
  justify-content: center;
  padding: 40px;
}

.container {
  background: white;
  border-radius: 16px;
  border: 1px solid #ccc;
  padding: 24px 32px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

h1 {
  font-size: 22px;
  font-weight: 700;
  margin: 0;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #ccc;
  margin-bottom: 16px;
}

.tab {
  background: none;
  border: none;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: border-color 0.2s;
}

.tab.active {
  border-bottom: 2px solid #007aff;
  font-weight: 600;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

input[type="number"],
input[type="color"] {
  padding: 6px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 14px;
  width: 120px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 12px;
}

button#save, button#reset {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  background: #007aff;
  color: white;
  white-space: nowrap;
}
button#save:hover, button#reset:hover {
  background: #005ecb;
}

#status {
  align-self: center;
  font-size: 13px;
  color: #007aff;
}

/* Ko-Fi Shake */
@keyframes shake {
  0%, 100% { transform: rotate(2deg); }
  25% { transform: rotate(1deg); }
  50% { transform: rotate(-1deg); }
  75% { transform: rotate(1deg); }
}
#kofi-widget {
  animation: shake 0.5s ease-in-out 6;
}