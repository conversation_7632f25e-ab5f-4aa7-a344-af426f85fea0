.container {
  width: 100%;
  max-width: 800px;
}

html,
body {
  width: 100%;
  min-height: 250px;
  max-width: 1000px;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border-radius: 12px;
  background: #f5f5f7;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "San Francisco", "Segoe UI",
    <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  width: 500px;
  padding: 20px;
  background: #f5f5f7;
  color: #1d1d1f;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#top-bar {
  position: relative;
  width: 100%;
  min-height: 36px;
}

#title-left {
  position: absolute;
  top: 6px;
  left: 12px;
}

#title-left h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
}

#top-buttons {
  position: absolute;
  top: 6px;
  right: 12px;
  display: flex;
  gap: 6px;
}

#top-buttons button {
  font-size: 10px;
  background: #f3f3f8;
  border: none;
  border-radius: 6px;
  padding: 4px 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

#top-buttons button:hover {
  background: #d1d1d6;
}

input {
  width: 95%;
  padding: 10px;
  margin: 12px 0;
  border: 1px solid #ccc;
  border-radius: 10px;
  background-color: #fff;
  font-size: 14px;
  transition: box-shadow 0.2s ease;
}

input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  padding: 10px 12px;
  margin-bottom: 8px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

li .task-text {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  white-space: normal;
}

li .task-url {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

li:hover {
  background-color: #f0f0f5;
}

li input.edit-input {
  flex: 1;
  padding: 8px;
  margin-right: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

li button {
  margin-left: 4px;
  background: #e5e5ea;
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

li button:hover {
  background: #d1d1d6;
}

li.dragging {
  opacity: 0.5;
}

.completed {
  text-decoration: line-through;
  color: #8e8e93;
}

.priority-red {
  color: var(--color-priority-red, #ff3b30);
}

.priority-yellow {
  color: var(--color-priority-yellow, #ffcc00);
}

.priority-green {
  color: var(--color-priority-green, #34c759);
}

.priority-black {
  color: #1c1c1e;
}

#task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
  margin-bottom: 16px;
}

#back-button,
#clear-completed {
  background: #e5e5ea;
  border: none;
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

#back-button:hover,
#clear-completed:hover {
  background: #d1d1d6;
}

h1,
h3 {
  font-weight: 600;
  margin: 0;
}

select {
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid #ccc;
  background-color: #fff;
  font-family: inherit;
  appearance: none;
}

select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.button-group {
  display: flex;
  flex-shrink: 0;
  gap: 4px;
  align-items: center;
  justify-content: flex-end;
}

.button-group button,
.button-group select {
  padding: 4px 6px;
  font-size: 12px;
  border-radius: 6px;
  background: #e5e5ea;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.button-group button:hover,
.button-group select:hover {
  background: #d1d1d6;
}

.button-group {
  opacity: 0.5;
  transition: opacity 0.15s ease;
}

li:hover .button-group {
  opacity: 1;
}

#main-view,
#group-view {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

button svg {
  stroke: #1d1d1f;
  display: block;
}

button.icon {
  background: none;
  border: none;
  padding: 2px;
  font-size: 14px;
  color: inherit;
  opacity: 0.6;
}

button.icon:hover {
  opacity: 1;
}

select.priority-select {
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid #ccc;
  background-color: white;
  appearance: none;
  cursor: pointer;
}

select.priority-select option.priority-red {
  color: var(--color-priority-red, #ff3b30);
}

select.priority-select option.priority-yellow {
  color: var(--color-priority-yellow, #ffcc00);
}

select.priority-select option.priority-green {
  color: var(--color-priority-green, #34c759);
}

select.priority-select option.priority-black {
  color: #1c1c1e;
}

select.priority-select option:hover,
select.priority-select option:checked,
select.priority-select option:focus {
  background-color: white !important;
  color: inherit !important;
}

#help-modal {
  display: none;
  position: fixed;
  top: 20px;
  right: 20px;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  width: 300px;
  z-index: 10;
}

#close-help {
  position: absolute;
  top: 6px;
  right: 8px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: bold;
  color: #666;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

#close-help:hover {
  color: #1d1d1f;
}

a {
  color: #007aff;
  text-decoration: underline;
  cursor: pointer;
}

a.completed {
  text-decoration: line-through;
  color: #8e8e93;
}
