const defaultSettings = {
  width: 400,
  fontSize: 14,
  color1: "#ff3b30",
  color2: "#ffcc00",
  color3: "#34c759"
};

function applySettingsToStorage(config) {
  chrome.storage.sync.set({ zendoConfig: config }, () => {
    document.getElementById('status').textContent = '✔ Saved!';
    setTimeout(() => document.getElementById('status').textContent = '', 2000);
  });
}

function loadSettings() {
  chrome.storage.sync.get(['zendoConfig'], (res) => {
    const config = { ...defaultSettings, ...res.zendoConfig };
    document.getElementById('width').value = config.width;
    document.getElementById('fontSize').value = config.fontSize;
    document.getElementById('color1').value = config.color1;
    document.getElementById('color2').value = config.color2;
    document.getElementById('color3').value = config.color3;
  });
}

function getConfigFromInputs() {
  return {
    width: parseInt(document.getElementById('width').value, 10),
    fontSize: parseInt(document.getElementById('fontSize').value, 10),
    color1: document.getElementById('color1').value,
    color2: document.getElementById('color2').value,
    color3: document.getElementById('color3').value
  };
}

document.getElementById('save').addEventListener('click', () => {
  const config = getConfigFromInputs();
  applySettingsToStorage(config);
});

document.getElementById('reset').addEventListener('click', () => {
  document.getElementById('width').value = defaultSettings.width;
  document.getElementById('fontSize').value = defaultSettings.fontSize;
  document.getElementById('color1').value = defaultSettings.color1;
  document.getElementById('color2').value = defaultSettings.color2;
  document.getElementById('color3').value = defaultSettings.color3;

  applySettingsToStorage({ ...defaultSettings });
});

document.querySelectorAll('.tab').forEach(tab => {
  tab.addEventListener('click', () => {
    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(tc => tc.style.display = 'none');

    tab.classList.add('active');
    document.getElementById(tab.dataset.tab).style.display = 'flex';
  });
});

document.addEventListener('DOMContentLoaded', () => {
  loadSettings();

  document.getElementById('save').addEventListener('click', () => {
    const config = getConfigFromInputs();
    applySettingsToStorage(config);
  });

  document.getElementById('reset').addEventListener('click', () => {
    document.getElementById('width').value = defaultSettings.width;
    document.getElementById('fontSize').value = defaultSettings.fontSize;
    document.getElementById('color1').value = defaultSettings.color1;
    document.getElementById('color2').value = defaultSettings.color2;
    document.getElementById('color3').value = defaultSettings.color3;

    applySettingsToStorage({ ...defaultSettings });
  });

  document.querySelectorAll('.tab').forEach(tab => {
    tab.addEventListener('click', () => {
      document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(tc => tc.style.display = 'none');

      tab.classList.add('active');
      document.getElementById(tab.dataset.tab).style.display = 'flex';
    });
  });
});
