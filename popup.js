window.zendoConfig = {
  width: 400,
  fontSize: 12,
  color1: "#ff3b30",
  color2: "#ffcc00",
  color3: "#34c759",
};

const storageKey = "todoGroups";

const mainView = document.getElementById("main-view");
const groupView = document.getElementById("group-view");

const groupList = document.getElementById("group-list");
const newGroupInput = document.getElementById("new-group");

const groupTitle = document.getElementById("group-title");
const newTaskInput = document.getElementById("new-task");
const taskList = document.getElementById("task-list");
const backButton = document.getElementById("back-button");

let dynamicStyleTag = null;

function applyConfig(config) {
  if (config.width) {
    document.body.style.width = `${config.width}px`;
  }
  if (config.fontSize) {
    document.querySelectorAll("#group-list li, #task-list li").forEach((el) => {
      el.style.fontSize = `${config.fontSize}px`;
    });
  }

  if (dynamicStyleTag) {
    dynamicStyleTag.remove();
  }

  const css = document.createElement("style");
  css.innerHTML = `
    :root {
      --color-priority-red: ${config.color1 || "#ff3b30"};
      --color-priority-yellow: ${config.color2 || "#ffcc00"};
      --color-priority-green: ${config.color3 || "#34c759"};
    }

    .priority-red { color: var(--color-priority-red) !important; }
    .priority-yellow { color: var(--color-priority-yellow) !important; }
    .priority-green { color: var(--color-priority-green) !important; }

    select.priority-select option.priority-red { color: var(--color-priority-red) !important; }
    select.priority-select option.priority-yellow { color: var(--color-priority-yellow) !important; }
    select.priority-select option.priority-green { color: var(--color-priority-green) !important; }
  `;
  document.head.appendChild(css);
  dynamicStyleTag = css;
}

const pencilBtn = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9" /><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4 12.5-12.5z" /></svg>`;

let currentGroup = null;

function save(groups) {
  chrome.storage.sync.set({ [storageKey]: groups });
}

function load(callback) {
  chrome.storage.sync.get([storageKey], (result) => {
    callback(result[storageKey] || {});
  });
}

function renderGroupList(groups) {
  groupList.innerHTML = "";

  for (const groupName in groups) {
    const li = document.createElement("li");
    if (window.zendoConfig?.fontSize) {
      li.style.fontSize = `${window.zendoConfig.fontSize}px`;
    }

    li.textContent = groupName;
    li.style.cursor = "pointer";
    li.addEventListener("click", () => {
      currentGroup = groupName;
      switchToGroupView(groups);
    });

    li.draggable = true;

    li.addEventListener("dragstart", (e) => {
      e.dataTransfer.setData("text/plain", groupName);
      li.classList.add("dragging");
    });

    li.addEventListener("dragend", () => {
      li.classList.remove("dragging");
    });

    li.addEventListener("dragover", (e) => {
      e.preventDefault();
    });

    li.addEventListener("drop", (e) => {
      e.preventDefault();
      const fromGroup = e.dataTransfer.getData("text/plain");
      const toGroup = groupName;

      const reordered = {};
      const keys = Object.keys(groups);
      const fromIndex = keys.indexOf(fromGroup);
      const toIndex = keys.indexOf(toGroup);

      keys.splice(toIndex, 0, keys.splice(fromIndex, 1)[0]);
      keys.forEach((k) => (reordered[k] = groups[k]));

      save(sanitize(reordered));
      renderGroupList(reordered);
    });

    const del = document.createElement("button");
    del.textContent = "x";
    del.addEventListener("click", (e) => {
      e.stopPropagation();
      delete groups[groupName];
      save(sanitize(groups));
      renderGroupList(groups);
    });

    li.appendChild(del);
    groupList.appendChild(li);
  }
}

function renderTasks(tasks, groups) {
  document.getElementById("current-group-name").textContent = currentGroup;
  taskList.innerHTML = "";

  tasks.forEach((task, index) => {
    const li = document.createElement("li");
    li.draggable = true;
    const editBtn = document.createElement("button");
    editBtn.innerHTML = pencilBtn;
    editBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      tasks[index].editing = true;
      save(sanitize(groups));
      if (groups[currentGroup]) {
        renderTasks(groups[currentGroup], groups);
      }
    });
    li.appendChild(editBtn);

    const copyBtn = document.createElement("button");
    copyBtn.textContent = "🗐";
    copyBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      navigator.clipboard.writeText(task.text);
    });
    li.appendChild(copyBtn);

    const prioritySelect = document.createElement("select");
    prioritySelect.className = "priority-select";

    ["red", "yellow", "green", "black"].forEach((color) => {
      const option = document.createElement("option");
      option.value = color;
      option.textContent = "●";
      option.className = `priority-${color}`;
      if (task.priority === color) option.selected = true;
      prioritySelect.appendChild(option);
    });

    prioritySelect.addEventListener("change", () => {
      task.priority = prioritySelect.value;
      save(sanitize(groups));
      if (groups[currentGroup]) {
        renderTasks(groups[currentGroup], groups);
      }
    });
    li.appendChild(prioritySelect);

    const delBtn = document.createElement("button");
    delBtn.textContent = "x";
    delBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      tasks.splice(index, 1);
      save(sanitize(groups));
      if (groups[currentGroup]) {
        renderTasks(groups[currentGroup], groups);
      }
    });
    li.appendChild(delBtn);

    li.addEventListener("dragstart", (e) => {
      li.classList.add("dragging");
      e.dataTransfer.setData("text/plain", index);
    });

    li.addEventListener("dragend", () => {
      li.classList.remove("dragging");
    });

    li.addEventListener("dragover", (e) => {
      e.preventDefault();
    });

    li.addEventListener("drop", (e) => {
      e.preventDefault();
      const fromIndex = parseInt(e.dataTransfer.getData("text/plain"), 10);
      const toIndex = index;
      const moved = tasks.splice(fromIndex, 1)[0];
      tasks.splice(toIndex, 0, moved);
      save(sanitize(groups));
      if (groups[currentGroup]) {
        renderTasks(groups[currentGroup], groups);
      }
    });

    const buttonGroup = document.createElement("div");
    buttonGroup.className = "button-group";

    buttonGroup.appendChild(editBtn);
    buttonGroup.appendChild(copyBtn);
    buttonGroup.appendChild(prioritySelect);
    buttonGroup.appendChild(delBtn);

    li.appendChild(buttonGroup);

    if (task.editing) {
      const textarea = document.createElement("textarea");
      textarea.value = task.text;
      textarea.className = "edit-input";
      textarea.style.resize = "vertical";
      textarea.style.width = "100%";
      textarea.rows = 3;

      textarea.addEventListener("input", () => {
        textarea.style.height = "auto";
        textarea.style.height = textarea.scrollHeight + "px";
      });

      textarea.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          tasks[index].text = textarea.value.trim();
          delete tasks[index].editing;
          save(sanitize(groups));
          if (groups[currentGroup]) {
            renderTasks(groups[currentGroup], groups);
          }
        } else if (e.key === "Escape") {
          e.preventDefault();
          e.stopPropagation();
          delete tasks[index].editing;
          if (groups[currentGroup]) {
            renderTasks(groups[currentGroup], groups);
          }
        }
      });

      li.appendChild(textarea);

      const cancelBtn = document.createElement("button");
      cancelBtn.textContent = "↩ Cancel";
      cancelBtn.title = "Cancel edit";
      cancelBtn.style.fontSize = "12px";
      cancelBtn.style.padding = "4px 6px";
      cancelBtn.style.borderRadius = "6px";
      cancelBtn.style.border = "none";
      cancelBtn.style.background = "#e5e5ea";
      cancelBtn.style.cursor = "pointer";

      cancelBtn.addEventListener("click", () => {
        delete tasks[index].editing;
        renderTasks(groups[currentGroup], groups);
      });

      const buttonGroup = document.createElement("div");
      buttonGroup.className = "button-group";
      buttonGroup.appendChild(cancelBtn);
      buttonGroup.appendChild(copyBtn);
      buttonGroup.appendChild(prioritySelect);
      buttonGroup.appendChild(delBtn);
      li.appendChild(buttonGroup);
    } else {
      let isUrl = false;
      try {
        const url = new URL(task.text);
        isUrl = url.protocol === "http:" || url.protocol === "https:";
      } catch (e) {
        isUrl = false;
      }

      let span;
      if (isUrl) {
        span = document.createElement("a");
        span.href = task.text;
        span.target = "_blank";
        span.rel = "noopener noreferrer";
        span.textContent = task.text;
        span.className = "task-url";
      } else {
        span = document.createElement("span");
        span.textContent = task.text;
        span.className = "task-text";

        span.addEventListener("click", () => {
          task.done = !task.done;
          save(sanitize(groups));
          renderTasks(groups[currentGroup], groups);
        });
      }

      span.style.flex = "1";
      if (task.done) span.classList.add("completed");

      if (task.priority) {
        span.classList.add(`priority-${task.priority}`);
      }

      span.addEventListener("click", () => {
        task.done = !task.done;
        save(sanitize(groups));
        if (groups[currentGroup]) {
          renderTasks(groups[currentGroup], groups);
        }
      });

      li.appendChild(span);

      buttonGroup.appendChild(editBtn);
      buttonGroup.appendChild(copyBtn);
      buttonGroup.appendChild(prioritySelect);
      buttonGroup.appendChild(delBtn);
      li.appendChild(buttonGroup);
    }

    if (window.zendoConfig?.fontSize) {
      li.style.fontSize = `${window.zendoConfig.fontSize}px`;
    }

    taskList.appendChild(li);
  });

  // Clear Completed Button
  document.getElementById("clear-completed").onclick = () => {
    const filtered = tasks.filter((t) => !t.done);
    groups[currentGroup] = filtered;
    save(sanitize(groups));
    renderTasks(filtered, groups);
  };
}

function switchToMainView(groups) {
  groupView.style.display = "none";
  mainView.style.display = "block";
  document.getElementById("task-header").style.display = "none";
  renderGroupList(groups);
}

function switchToGroupView(groups) {
  mainView.style.display = "none";
  groupView.style.display = "block";
  document.getElementById("task-header").style.display = "flex";
  renderTasks(groups[currentGroup], groups);
  renderGroupNameWrapper();
}

function sanitize(groups) {
  const clean = {};
  for (const group in groups) {
    clean[group] = groups[group].map((task) => {
      const { text, done, priority } = task;
      return { text, done, priority };
    });
  }
  return clean;
}

function renderGroupNameWrapper() {
  const wrapper = document.getElementById("group-name-wrapper");
  wrapper.innerHTML = "";

  const nameHeading = document.createElement("h3");
  nameHeading.id = "current-group-name";
  nameHeading.textContent = currentGroup;
  nameHeading.style.margin = "0";

  const editBtn = document.createElement("button");
  editBtn.id = "edit-group-name";
  editBtn.title = "Edit group name";
  editBtn.style.border = "none";
  editBtn.innerHTML = pencilBtn;

  editBtn.onclick = initGroupEdit;

  wrapper.appendChild(nameHeading);
  wrapper.appendChild(editBtn);
}

function initGroupEdit() {
  const wrapper = document.getElementById("group-name-wrapper");
  wrapper.innerHTML = "";

  const input = document.createElement("input");
  input.type = "text";
  input.value = currentGroup;
  input.style.flexGrow = "1";

  const saveBtn = document.createElement("button");
  saveBtn.textContent = "✔";
  saveBtn.title = "Save group name";

  saveBtn.onclick = () => {
    const newName = input.value.trim();
    if (!newName || newName === currentGroup) {
      load(renderGroupNameWrapper);
      return;
    }
    load((groups) => {
      if (groups[newName]) return alert("Group already exists!");
      groups[newName] = groups[currentGroup];
      delete groups[currentGroup];
      currentGroup = newName;
      save(sanitize(groups));
      renderGroupNameWrapper();
    });
  };

  wrapper.appendChild(input);
  wrapper.appendChild(saveBtn);

  input.focus();
  input.select();
}

newGroupInput.addEventListener("keydown", (e) => {
  if (e.key === "Enter" && newGroupInput.value.trim()) {
    const name = newGroupInput.value.trim();
    load((groups) => {
      if (!groups[name]) {
        groups[name] = [];
        save(sanitize(groups));
        renderGroupList(groups);
      }
    });
    newGroupInput.value = "";
  }
});

newTaskInput.addEventListener("keydown", (e) => {
  if (e.key === "Enter" && newTaskInput.value.trim()) {
    const text = newTaskInput.value.trim();
    load((groups) => {
      groups[currentGroup].push({ text, done: false, priority: "black" });
      save(sanitize(groups));
      renderTasks(groups[currentGroup], groups);
    });
    newTaskInput.value = "";
  }
});

backButton.addEventListener("click", () => {
  load(switchToMainView);
});

document
  .getElementById("edit-group-name")
  .addEventListener("click", initGroupEdit);

document.getElementById("help-button").addEventListener("click", () => {
  document.getElementById("help-modal").style.display = "block";
});

document.getElementById("close-help").addEventListener("click", () => {
  document.getElementById("help-modal").style.display = "none";
});

document.getElementById("settings-button").addEventListener("click", () => {
  chrome.runtime.openOptionsPage();
});

document.addEventListener("DOMContentLoaded", () => {
  chrome.storage.sync.get(["zendoConfig"], (result) => {
    const config = result.zendoConfig || {};
    applyConfig(config);

    load((groups) => {
      for (const g in groups) {
        groups[g].forEach((t) => delete t.editing);
      }
      switchToMainView(groups);
    });
  });

  newGroupInput.addEventListener("keydown", (e) => {
    if (e.key === "Enter" && newGroupInput.value.trim()) {
      const name = newGroupInput.value.trim();
      load((groups) => {
        if (!groups[name]) {
          groups[name] = [];
          save(sanitize(groups));
          renderGroupList(groups);
        }
      });
      newGroupInput.value = "";
    }
  });

  newTaskInput.addEventListener("keydown", (e) => {
    if (e.key === "Enter" && newTaskInput.value.trim()) {
      const text = newTaskInput.value.trim();
      load((groups) => {
        groups[currentGroup].push({ text, done: false, priority: "black" });
        save(sanitize(groups));
        renderTasks(groups[currentGroup], groups);
      });
      newTaskInput.value = "";
    }
  });

  backButton.addEventListener("click", () => {
    load(switchToMainView);
  });

  document.getElementById("help-button").addEventListener("click", () => {
    document.getElementById("help-modal").style.display = "block";
  });

  document.getElementById("close-help").addEventListener("click", () => {
    document.getElementById("help-modal").style.display = "none";
  });

  document.getElementById("settings-button").addEventListener("click", () => {
    chrome.runtime.openOptionsPage();
  });
});

chrome.storage.onChanged.addListener((changes, areaName) => {
  if (areaName === "sync" && changes.zendoConfig) {
    const newConfig = changes.zendoConfig.newValue;
    window.zendoConfig = newConfig;
    applyConfig(newConfig);

    load((groups) => {
      if (groupView.style.display === "block") {
        renderTasks(groups[currentGroup], groups);
      } else {
        renderGroupList(groups);
      }
    });
  }
});
